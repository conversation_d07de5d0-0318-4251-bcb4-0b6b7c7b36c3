/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft Yahei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 25%, #f7931e 50%, #ffd23f 75%, #06ffa5 100%);
  min-height: 100vh;
  padding: 20px;
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 154, 86, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(6, 255, 165, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 210, 63, 0.2) 0%, transparent 50%);
  z-index: -1;
  animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

/* 主容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* 头部图片样式 */
.header-image {
  width: 100%;
  max-width: 900px;
  margin: 0 auto 30px auto;
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 12px 40px rgba(0,0,0,0.15),
    0 4px 12px rgba(0,0,0,0.1);
  position: relative;
  animation: fadeInDown 1s ease-out;
}

.header-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 154, 86, 0.1) 0%,
    rgba(6, 255, 165, 0.1) 100%);
  z-index: 1;
  pointer-events: none;
}

.header-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 0;
}

.header-image:hover img {
  transform: scale(1.02);
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题样式 */
h1 {
  font-size: clamp(24px, 5vw, 42px);
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 40px;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
  position: relative;
  animation: titleGlow 3s ease-in-out infinite alternate;
}

h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #ff6b35, #ffd23f);
  border-radius: 2px;
  animation: underlineExpand 2s ease-out;
}

@keyframes titleGlow {
  0% { text-shadow: 2px 2px 4px rgba(0,0,0,0.1); }
  100% { text-shadow: 2px 2px 20px rgba(255, 107, 53, 0.3); }
}

@keyframes underlineExpand {
  0% { width: 0; }
  100% { width: 100px; }
}

/* 区域卡片样式 */
.section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow:
    0 8px 32px rgba(0,0,0,0.1),
    0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid rgba(255,255,255,0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s ease;
}

.section:hover {
  transform: translateY(-5px);
  box-shadow:
    0 12px 40px rgba(0,0,0,0.15),
    0 4px 12px rgba(0,0,0,0.1);
}

.section:hover::before {
  left: 100%;
}

.section h2 {
  font-size: 24px;
  color: #2c3e50;
  margin-bottom: 25px;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

/* 按钮网格布局 */
.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

/* 基础按钮样式 */
button {
  padding: 15px 25px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 15px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  box-shadow:
    0 4px 15px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
  text-align: center;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
  transition: left 0.5s ease;
}

button:hover::before {
  left: 100%;
}

button:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 8px 25px rgba(0,0,0,0.15),
    inset 0 1px 0 rgba(255,255,255,0.7);
}

button:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

button i {
  margin-right: 8px;
  font-size: 18px;
  transition: transform 0.3s ease;
}

button:hover i {
  transform: scale(1.2) rotate(5deg);
}

/* 名言区域样式 */
#quotes {
  border-left: 5px solid #ff6b35;
}

#quotes h2 {
  color: #ff6b35;
}

#quotes button {
  background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

#quotes button:hover {
  background: linear-gradient(135deg, #ff8a46 0%, #ff5b25 100%);
  box-shadow:
    0 8px 25px rgba(255, 107, 53, 0.4),
    inset 0 1px 0 rgba(255,255,255,0.3);
}

/* 语音区域样式 */
#speech {
  border-left: 5px solid #06ffa5;
}

#speech h2 {
  color: #06ffa5;
}

#speech button {
  background: linear-gradient(135deg, #06ffa5 0%, #00d4aa 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

#speech button:hover {
  background: linear-gradient(135deg, #05e695 0%, #00c49a 100%);
  box-shadow:
    0 8px 25px rgba(6, 255, 165, 0.4),
    inset 0 1px 0 rgba(255,255,255,0.3);
}

/* 音乐区域样式 */
#music {
  border-left: 5px solid #ffd23f;
}

#music h2 {
  color: #f7931e;
}

#music button {
  background: linear-gradient(135deg, #ffd23f 0%, #f7931e 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

#music button:hover {
  background: linear-gradient(135deg, #ffc92f 0%, #e7831e 100%);
  box-shadow:
    0 8px 25px rgba(255, 210, 63, 0.4),
    inset 0 1px 0 rgba(255,255,255,0.3);
}

/* 功能区域样式 */
#controls {
  border-left: 5px solid #6c5ce7;
}

#controls h2 {
  color: #6c5ce7;
}

#controls button {
  background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  font-weight: 600;
}

#controls button:hover {
  background: linear-gradient(135deg, #9287fd 0%, #5c4ce7 100%);
  box-shadow:
    0 8px 25px rgba(108, 92, 231, 0.4),
    inset 0 1px 0 rgba(255,255,255,0.3);
}

/* 特殊按钮效果 */
#loop.active, #random.active {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(0, 184, 148, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(0, 184, 148, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 184, 148, 0); }
}

/* 音量控制样式 */
.volume-control {
  margin: 25px 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.volume-control label {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #6c5ce7;
  margin-bottom: 15px;
}

.volume-control label i {
  margin-right: 8px;
  font-size: 18px;
}

.volume-slider-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

#volume-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

#volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(108, 92, 231, 0.3);
  transition: all 0.3s ease;
}

#volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.5);
}

#volume-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(108, 92, 231, 0.3);
}

#volume-value {
  font-weight: 600;
  color: #6c5ce7;
  min-width: 40px;
  text-align: center;
}

/* 播放统计面板样式 */
.stats-panel {
  margin: 25px 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.stats-panel h3 {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #6c5ce7;
  margin-bottom: 20px;
}

.stats-panel h3 i {
  margin-right: 8px;
  font-size: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.2);
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
}

.reset-btn {
  background: linear-gradient(135deg, #e17055 0%, #d63031 100%) !important;
  color: white !important;
  font-size: 14px !important;
  padding: 10px 20px !important;
  margin: 0 auto;
  display: block;
}

.reset-btn:hover {
  background: linear-gradient(135deg, #d1634d 0%, #c62d2f 100%) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    padding: 15px;
  }

  .header-image {
    margin-bottom: 20px;
    border-radius: 15px;
  }

  .section {
    padding: 20px;
    margin-bottom: 20px;
  }

  .button-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }

  button {
    padding: 12px 20px;
    font-size: 14px;
    min-height: 50px;
  }

  h1 {
    margin-bottom: 30px;
  }

  .section h2 {
    font-size: 20px;
    margin-bottom: 20px;
  }

  .volume-control, .stats-panel {
    padding: 15px;
    margin: 20px 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .stat-item {
    padding: 12px;
  }

  .volume-slider-container {
    flex-direction: column;
    gap: 10px;
  }

  #volume-slider {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .button-grid {
    grid-template-columns: 1fr;
  }

  button {
    padding: 15px;
    font-size: 16px;
  }
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section {
  animation: fadeInUp 0.6s ease-out;
}

.section:nth-child(1) { animation-delay: 0.1s; }
.section:nth-child(2) { animation-delay: 0.2s; }
.section:nth-child(3) { animation-delay: 0.3s; }
.section:nth-child(4) { animation-delay: 0.4s; }

/* 音频播放器隐藏样式 */
#player {
  display: none;
}
