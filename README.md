# 四川芬达 · 阿坤语音交互项目

这是一个基于 HTML + JavaScript 实现的语音交互小项目。包含四个区域：

- 🎤 名言区域：播放阿坤的"经典语录"
- 🗣️ 语音区域：四川芬达语音游戏片段
- 🎵 音乐区域：音乐化趣味改编
- 🛠️ 功能区域：控制播放（单曲循环、全部停止）

## 使用方法

1. 将 MP3 音频资源按区域分类存放在 `audio/` 目录下
2. 双击 `index.html` 即可在浏览器中打开

## 项目结构

```
├── index.html
├── style.css
├── script.js
├── README.md
├── images/
│   └── header.jpg
└── audio/
    ├── quotes/     (名言区域)
    ├── speech/     (语音区域)
    └── music/      (音乐区域)
```

## 🎯 功能特色

- 🎤 **名言区域** - 收录阿坤的经典名言语录
- 🗣️ **语音区域** - 日常语音交互内容
- 🎵 **音乐区域** - 音乐相关的有趣内容
- 🛠️ **功能控制** - 完整的播放控制功能
  - 🔄 单曲循环模式
  - 🎲 随机播放模式
  - 🔊 音量控制滑块
  - 📊 播放统计面板
  - ⏹️ 停止播放功能

## 🎨 设计特点

- 现代化的渐变背景设计（橙色系呼应芬达品牌）
- 响应式布局，完美支持移动端
- 流畅的动画效果和交互反馈
- 卡片式布局，视觉层次清晰
- 不同区域采用不同的主题色彩
- 毛玻璃效果和动态背景装饰

## 🛠️ 技术实现

- 纯HTML5 + CSS3 + JavaScript
- 使用Remix Icon图标库
- CSS Grid布局和Flexbox
- CSS动画和过渡效果
- 音频播放API
- 响应式设计

## 🎵 音频文件命名规范

### 文件夹结构
```
audio/
├── quotes/     (名言区域)
├── speech/     (语音区域)
└── music/      (音乐区域)
```

### 文件命名映射表

#### 🎤 名言区域 (audio/quotes/)
| 文件名 | 内容 |
|--------|------|
| 01.mp3 | 没座 |
| 02.mp3 | 没错 |
| 03.mp3 | A座 |
| 04.mp3 | 你礼貌吗 |
| 05.mp3 | 你要干什么 |
| 06.mp3 | 无所谓 |
| 07.mp3 | 你是艺术家 |
| 08.mp3 | 阿坤是歌手 |
| 09.mp3 | 我不这么认为 |
| 10.mp3 | 因为我就是顶尖 |
| 11.mp3 | 哦哦哦哦~ |

#### 🗣️ 语音区域 (audio/speech/)
| 文件名 | 内容 |
|--------|------|
| 01.mp3 | 丢 |
| 02.mp3 | 啊丢 |
| 03.mp3 | 啊丢手绢 |
| 04.mp3 | 轻轻放在小朋友的后边 |
| 05.mp3 | 大家不要告诉他 |

#### 🎵 音乐区域 (audio/music/)
| 文件名 | 内容 |
|--------|------|
| 01.mp3 | 他们朝我扔粑粑 |
| 02.mp3 | 我拿粑粑做蛋挞 |
| 03.mp3 | 他们朝我扔白菜 |
| 04.mp3 | 我拿白菜炒盘菜 |
| 05.mp3 | 他们朝我扔烟头 |
| 06.mp3 | 我捡起烟头抽两口 |
| 07.mp3 | 哦哦哦哦~ |

## 🚀 快速开始

### 1. 设置头部图片
将您的图片文件重命名为 `header.jpg` 并放入 `images` 文件夹中：
```
images/header.jpg  (推荐尺寸: 900×383)
```

### 2. 准备音频文件
- 按照上述映射表准备23个MP3文件
- 使用数字命名格式（01.mp3, 02.mp3...）
- 放入对应的文件夹中

### 3. 启动项目
直接在浏览器中打开 `index.html` 文件

## 🎮 使用方法

### 基础播放功能
1. **播放音频**：点击任意按钮播放对应音频
2. **停止播放**：点击"全部停止"按钮停止当前播放
3. **视觉反馈**：播放中的按钮会有脉冲动画效果

### 播放模式控制
4. **单曲循环**：点击"单曲循环"按钮开启/关闭循环模式
5. **随机播放**：点击"随机播放"按钮开启随机播放模式
   - 随机模式下音频结束会自动播放下一首随机音频
   - 循环模式和随机模式互斥，开启一个会关闭另一个

### 音量控制
6. **调节音量**：使用音量滑块调节播放音量（0-100%）
7. **音量记忆**：音量设置会自动保存到本地

### 播放统计
8. **查看统计**：实时查看播放统计数据
   - 总播放次数
   - 当前播放的音频
   - 最受欢迎的音频
9. **重置统计**：点击"重置统计"按钮清空所有统计数据

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📄 许可证

本项目仅供学习和娱乐使用。
本项目完全由Augement开发制作
---

**注意**：本项目包含的音频内容版权归原作者所有，请勿用于商业用途。
