<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="四川芬达阿坤语音交互项目 - 经典语录音频播放器" />
  <meta name="keywords" content="四川芬达,阿坤,语音交互,音频播放" />
  <title>四川芬达 · 阿坤语音交互项目</title>
  <link rel="stylesheet" href="style.css" />
  <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body>
  <div class="container">
    <div class="header-image">
      <img src="images/header.jpg" alt="四川芬达阿坤" />
    </div>
    <h1>四川芬达 · 阿坤语音交互项目</h1>

    <div class="section" id="quotes">
      <h2>🎤 是龙你得盘着，是虎你得卧着，听四川芬达演唱会你得站着，因为</h2>
      <div class="button-grid">
        <button data-audio="audio/quotes/01.mp3"><i class="ri-mic-line"></i> 没座</button>
        <button data-audio="audio/quotes/02.mp3"><i class="ri-check-line"></i> 没错</button>
        <button data-audio="audio/quotes/03.mp3"><i class="ri-building-line"></i> A座</button>
        <button data-audio="audio/quotes/04.mp3"><i class="ri-question-line"></i> 你礼貌吗</button>
        <button data-audio="audio/quotes/05.mp3"><i class="ri-alert-line"></i> 你要干什么</button>
        <button data-audio="audio/quotes/06.mp3"><i class="ri-emotion-normal-line"></i> 无所谓</button>
        <button data-audio="audio/quotes/07.mp3"><i class="ri-palette-line"></i> 你是艺术家</button>
        <button data-audio="audio/quotes/08.mp3"><i class="ri-music-line"></i> 阿坤是歌手</button>
        <button data-audio="audio/quotes/09.mp3"><i class="ri-close-line"></i> 我不这么认为</button>
        <button data-audio="audio/quotes/10.mp3"><i class="ri-star-line"></i> 我就是顶尖</button>
        <button data-audio="audio/quotes/11.mp3"><i class="ri-music-2-line"></i> 哦哦哦哦~</button>
      </div>
    </div>

    <div class="section" id="speech">
      <h2>🗣️ 语音</h2>
      <div class="button-grid">
        <button data-audio="audio/speech/01.mp3"><i class="ri-arrow-right-line"></i> 丢</button>
        <button data-audio="audio/speech/02.mp3"><i class="ri-arrow-right-line"></i> 啊丢</button>
        <button data-audio="audio/speech/03.mp3"><i class="ri-t-shirt-line"></i> 啊丢手绢</button>
        <button data-audio="audio/speech/04.mp3"><i class="ri-heart-line"></i> 轻轻放在小朋友的后边</button>
        <button data-audio="audio/speech/05.mp3"><i class="ri-volume-mute-line"></i> 大家不要告诉他</button>
      </div>
    </div>

    <div class="section" id="music">
      <h2>🎵 音乐</h2>
      <div class="button-grid">
        <button data-audio="audio/music/01.mp3"><i class="ri-restaurant-line"></i> 他们朝我扔粑粑</button>
        <button data-audio="audio/music/02.mp3"><i class="ri-restaurant-line"></i> 我拿粑粑做蛋挞</button>
        <button data-audio="audio/music/03.mp3"><i class="ri-restaurant-line"></i> 他们朝我扔白菜</button>
        <button data-audio="audio/music/04.mp3"><i class="ri-restaurant-line"></i> 我拿白菜炒盘菜</button>
        <button data-audio="audio/music/05.mp3"><i class="ri-restaurant-line"></i> 他们朝我扔烟头</button>
        <button data-audio="audio/music/06.mp3"><i class="ri-fire-line"></i> 我捡起烟头抽两口</button>
        <button data-audio="audio/music/07.mp3"><i class="ri-disc-line"></i> 哦哦哦哦~</button>
      </div>
    </div>

    <div class="section" id="controls">
      <h2>🛠️ 功能控制</h2>

      <!-- 播放控制按钮 -->
      <div class="button-grid">
        <button id="loop"><i class="ri-loop-left-line"></i> 单曲循环</button>
        <button id="random"><i class="ri-shuffle-line"></i> 随机播放</button>
        <button id="stop"><i class="ri-stop-circle-line"></i> 全部停止</button>
      </div>

      <!-- 音量控制 -->
      <div class="volume-control">
        <label for="volume-slider">
          <i class="ri-volume-up-line"></i> 音量控制
        </label>
        <div class="volume-slider-container">
          <input type="range" id="volume-slider" min="0" max="100" value="50" />
          <span id="volume-value">50%</span>
        </div>
      </div>

      <!-- 播放统计 -->
      <div class="stats-panel">
        <h3><i class="ri-bar-chart-line"></i> 播放统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">总播放次数</span>
            <span class="stat-value" id="total-plays">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">当前播放</span>
            <span class="stat-value" id="current-playing">无</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最受欢迎</span>
            <span class="stat-value" id="most-popular">暂无数据</span>
          </div>
        </div>
        <button id="reset-stats" class="reset-btn">
          <i class="ri-refresh-line"></i> 重置统计
        </button>
      </div>
    </div>
  </div>

  <audio id="player" controls style="display:none;"></audio>
  <script src="script.js"></script>
</body>
</html>
